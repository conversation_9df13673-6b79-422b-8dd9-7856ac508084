#! /usr/bin/sh

# list network interface and its pci addr and driver.
# by zhengsw@17/12/20

OUT_FILE=.if_tbl

if [ -e $OUT_FILE ]
then
    echo "there is already exist file $OUT_FILE, exit"
    exit
fi

# clear it
>$OUT_FILE

INTERFACES=$(cat /proc/net/dev|grep ':'|grep -v 'lo'|grep -v 'vir'|grep -v 'sit'|awk -F":" '{print $1}'|tr -d ' ')
for IF in $INTERFACES ; do
    ethtool_info=$(ethtool -i $IF)
    #echo "$ethtool_info"
    if_bus=$(echo "$ethtool_info" | grep bus-info | cut -d ' ' -f2)
    driver=$(echo "$ethtool_info" | grep driver | cut -d ' ' -f2)
    printf "if=%-12s bus=%-14s driver=%s\n" $IF $if_bus $driver >>$OUT_FILE
done
