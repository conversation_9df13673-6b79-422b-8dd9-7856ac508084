#! /usr/bin/sh

cd $(dirname $0)
WORK_DIR=`pwd`
KO_DPDK_LINK_TO=./x86_64-native-linuxapp-gcc/kmod
KO_EXTRA_DIR=/lib/modules/`uname -r`/extra/etl_dpdk
KO_DPDK_LINK_FROM=kmod_centos_7.4_1708   #默认OS
BOOT_CFG_PATH=/boot/grub2/grub.cfg       #默认BIOS/EFI

# 判断 OS 发行版本
# 检测 openEuler 系统
if [ -f /etc/os-release ]; then
    openeuler_system=`grep "openEuler" /etc/os-release`
    if [ -n "$openeuler_system" ] ; then
        KO_DPDK_LINK_FROM=kmod_openEuler_24.03
    fi
fi

# 检测 Red Hat 系统
if [ -f /etc/redhat-release ]; then
    redhat_system=`grep "Red Hat" /etc/redhat-release`
    if [ -n "$redhat_system" ] ; then
        KO_DPDK_LINK_FROM=kmod_redhat_7.2
    fi
fi
echo "[KO_DPDK_LINK_FROM]: $KO_DPDK_LINK_FROM"

# 如果是 EFI启动[CentOS/Redhat]
if [ -f /boot/efi/EFI/centos/grub.cfg ]
then
    BOOT_CFG_PATH=/boot/efi/EFI/centos/grub.cfg
fi

if [ -f /boot/efi/EFI/redhat/grub.cfg ]
then
    BOOT_CFG_PATH=/boot/efi/EFI/redhat/grub.cfg
fi

echo "[grub.cfg] $BOOT_CFG_PATH"
# 配置 kmod link
ln -sf  $KO_DPDK_LINK_FROM $KO_DPDK_LINK_TO

# make brothers executable
chmod 755 *.sh

# gen .if_tbl
if [ ! -e .if_tbl ]
then
  ./eth_info_tbl.sh
fi
cat .if_tbl

# prepare ko modules
mkdir -p $KO_EXTRA_DIR
cp -vf $KO_DPDK_LINK_TO/*.ko $KO_EXTRA_DIR/
echo -e "igb_uio\nrte_kni" > /etc/modules-load.d/etl_dpdk.conf

# install ko modules
depmod
modprobe igb_uio
modprobe rte_kni

# set hugepage on kernel cmdline: 16GB
if [ ! -e ${WORK_DIR}/grub.bak ]
then
   cp -v  /etc/default/grub ${WORK_DIR}/grub.bak
fi

# 检测grub是否已经被修改
:<<!
if [ $? -ne 0 ]
then
    sed 's/GRUB_CMDLINE_LINUX="\(.*\)"/GRUB_CMDLINE_LINUX="\1 console=ttyS0,115200 default_hugepagesz=1G hugepagesz=1G hugepages=80"/' ${WORK_DIR}/grub.bak >/etc/default/grub
    grub2-mkconfig -o $BOOT_CFG_PATH
fi
!
#grep 'default_hugepagesz=1G hugepagesz=1G hugepages=20' grub
flag=`sed -n '/.*default_hugepagesz=1G hugepagesz=1G hugepages=80.*/p' /etc/default/grub`
if [ -z "${flag}" ]; then
    sed -i 's/GRUB_CMDLINE_LINUX="\(.*\)"/GRUB_CMDLINE_LINUX="\1 console=ttyS0,115200 default_hugepagesz=1G hugepagesz=1G hugepages=80"/g' /etc/default/grub
    #cp /etc/default/grub  ${WORK_DIR}/grub.bak
    grub2-mkconfig -o $BOOT_CFG_PATH
fi
