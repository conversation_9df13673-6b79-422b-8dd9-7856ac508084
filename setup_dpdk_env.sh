#! /usr/bin/sh
# 加载 dpdk 驱动，配置巨页，为网卡绑定 dpdk 网卡驱动
# 或者执行相反的操作，具体见 usage_str
# changlog:
# v0.7 09:33 2022/11/25: 修复当系统中存在桥接网卡时出现的 pci 中设备个数与 .if_tbl 中设备个数不一致
#                        导致无法正确解绑目标网卡问题.
# v0.6 09:00 2019/11/09: 不再支持单个网卡参数形式，不再支持 undo
#                      : 配置文件中默认网卡变量(ETH_NAME_LIST)支持列表形式，可配置多个网卡.
# v0.5 11:01 2018/12/17: 初始版本.

ETH_NAME_LIST="XXX YYY"                            # 列出需要绑定的网卡接口名，使用空格分隔

usage_str="usage:
  ./setup_dpdk_env.sh                              :加载 dpdk 驱动，配置巨页，按配置($ETH_NAME_LIST)绑定网卡
  ./setup_dpdk_env.sh -b eth1 eth2                 :加载 dpdk 驱动，配置 40G 巨页，分别绑定 eth1 和 eth2 网卡
  ./setup_dpdk_env.sh -m 20 -b eth1 eth2           :加载 dpdk 驱动，配置 20G 巨页，分别绑定 eth1 和 eth2 网卡
  ./setup_dpdk_env.sh -b                           :加载 dpdk 驱动，配置巨页，不绑定任何网卡
  ./setup_dpdk_env.sh -x eth1 eth2                 :解绑 eht1 和 eth2 为原有网卡驱动
  ./setup_dpdk_env.sh -x                           :解绑所有被绑定到 dpdk 驱动的网卡，移除 dpdk 驱动，移除巨页
  ./setup_dpdk_env.sh -c                           :查看当前巨页配置"

cd $(dirname $0)

# RTE_SDK
RTE_SDK=.
RTE_TARGET=x86_64-native-linuxapp-gcc

# HUGEPAGES params
HUGEPGSZ_IN_KB=`cat /proc/meminfo  | grep Hugepagesize | cut -d : -f 2 | tr -d ' '`
HUGEPGSZ_NUMBER=${HUGEPGSZ_IN_KB%kB}
HUGE_PAGE_TOTAL_SIZE_IN_KB=$((1024*1024*80))                        # 40GB
NUMA_HUGE_PAGE_CNT=$((HUGE_PAGE_TOTAL_SIZE_IN_KB/HUGEPGSZ_NUMBER))

# default eth addr and driver.
ETH_PCI_ADDR="---"
OLD_DRIVER_OF_IF="---"

function LOG_ERR
{
    echo -e "\033[1;31;40m"$*"\033[0m"
}

function LOG_WAR
{
    echo -e "\033[1;33;40m"$*"\033[0m"
}

function LOG_INFO
{
    echo -e "\033[1;32;40m"$*"\033[0m"
}

function LOG_DEBUG
{
    echo -e "\033[1;30;40m"$*"\033[0m"
}

function step
{
    if [ $? -ne 0 ]
    then
        LOG_ERR "previous action failed, exit."
        exit
    fi

    echo -n "step $1: "

    shift
    eval $*
}

#
# Unloads igb_uio.ko.
#
remove_igb_uio_module()
{
    /sbin/lsmod | grep -s igb_uio > /dev/null
    if [ $? -eq 0 ] ; then
         /sbin/rmmod igb_uio
    fi

    LOG_INFO "Unloading any existing DPDK UIO module"
}

#
# Loads new igb_uio.ko (and uio module if needed).
#
load_igb_uio_module()
{
    if [ ! -f $RTE_SDK/$RTE_TARGET/kmod/igb_uio.ko ];then
        LOG_ERR "## ERROR: Target does not have the DPDK UIO Kernel Module."
        LOG_ERR "       To fix, please try to rebuild target."
        exit
    fi

    #remove_igb_uio_module

    # UIO may be compiled into kernel, so it may not be an error if it can't
    # be loaded.
    # LOG_DEBUG "Loading DPDK UIO module"
    modprobe igb_uio
    if [ $? -ne 0 ] ; then
        LOG_ERR "## ERROR: Could not load kmod/igb_uio.ko."
        exit
    fi
    LOG_INFO "Load DPDK UIO module Successful!"
}

#
# Unloads the rte_kni.ko module.
#
remove_kni_module()
{
    /sbin/lsmod | grep -s rte_kni > /dev/null
    if [ $? -eq 0 ] ; then
         /sbin/rmmod rte_kni
    fi

    LOG_INFO "Unloading any existing DPDK KNI module"
}

#
# Loads the rte_kni.ko module.
#
load_kni_module()
{
    # LOG_DEBUG "Check that the KNI module is already built"
    if [ ! -f $RTE_SDK/$RTE_TARGET/kmod/rte_kni.ko ];then
        LOG_ERR "## ERROR: Target does not have the DPDK KNI Module."
        LOG_ERR "       To fix, please try to rebuild target."
        exit
    fi

    # Unload existing version if present.
    # remove_kni_module

    # Now try load the KNI module.
    # LOG_DEBUG "Loading DPDK KNI module"
    modprobe rte_kni
    if [ $? -ne 0 ] ; then
        LOG_ERR "## ERROR: Could not load kmod/rte_kni.ko."
        exit
    fi
    LOG_INFO "Load  DPDK KNI module Successful!"
}

#
# Removes all reserved hugepages.
#
clear_huge_pages()
{
    echo > .echo_tmp
    for d in /sys/devices/system/node/node? ; do
        echo "echo 0 > $d/hugepages/hugepages-${HUGEPGSZ_IN_KB}/nr_hugepages" >> .echo_tmp
    done

    sh .echo_tmp
    rm -f .echo_tmp

    remove_mnt_huge

    LOG_INFO "Remove currently reserved hugepages, Unmounting /mnt/huge and removing directory"
}

#
# Creates hugepage filesystem.
#
create_mnt_huge()
{
    mkdir -p /mnt/huge

    grep -s '/mnt/huge' /proc/mounts > /dev/null
    if [ $? -ne 0 ] ; then
         mount -t hugetlbfs nodev /mnt/huge
    fi
}

#
# Removes hugepage filesystem.
#
remove_mnt_huge()
{
    grep -s '/mnt/huge' /proc/mounts > /dev/null
    if [ $? -eq 0 ] ; then
         umount /mnt/huge
    fi

    if [ -d /mnt/huge ] ; then
         rm -R /mnt/huge
    fi
}

# Creates hugepages on specific NUMA nodes.
#
set_numa_pages()
{
    # clear_huge_pages

    Pages=$1

    # echo > .echo_tmp
    # for d in /sys/devices/system/node/node? ; do
    #     node=$(basename $d)
    #     echo "echo $Pages > $d/hugepages/hugepages-${HUGEPGSZ_IN_KB}/nr_hugepages" >> .echo_tmp
    # done

    echo > .echo_tmp
    node=$(basename $d)

    echo "echo $Pages > /sys/devices/system/node/node0/hugepages/hugepages-${HUGEPGSZ_IN_KB}/nr_hugepages" >> .echo_tmp
    sh .echo_tmp

    rm -f .echo_tmp

    echo > .echo_tmp
    node=$(basename $d)

    echo "echo 0 > /sys/devices/system/node/node1/hugepages/hugepages-${HUGEPGSZ_IN_KB}/nr_hugepages" >> .echo_tmp
    sh .echo_tmp

    rm -f .echo_tmp
    create_mnt_huge

    LOG_INFO "Reserving hugepages: $Pages, Creating /mnt/huge and mounting as hugetlbfs"
}

#
# show hugepages infos from /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages and
# /proc/meminfo.
#
show_numa_pages()
{
    LOG_INFO "show hugepages config:"

    LOG_WAR "cmdline:"
    echo "$(cat /proc/cmdline)"

    LOG_WAR "hugepages info:"
    for d in /sys/devices/system/node/node? ; do
        pagesPath=$d/hugepages/hugepages-${HUGEPGSZ_IN_KB}/nr_hugepages
        echo $pagesPath "$(cat $pagesPath)"
    done

    LOG_WAR "hugepages in /proc/meminfo:"
    echo "$(grep -ri huge /proc/meminfo)"
}

#
# Uses dpdk-devbind.py to move devices to work with igb_uio
#
bind_devices_to_igb_uio()
{
    if [ -d /sys/module/igb_uio ]; then
        PCI_PATH=$1
        ${RTE_SDK}/usertools/dpdk-devbind.py --force -b igb_uio $PCI_PATH || exit 1
        LOG_INFO "bind $PCI_PATH to igb_uio Successfully!"
    else
        LOG_ERR "# Please load the 'igb_uio' kernel module before querying or "
        LOG_ERR "# adjusting device bindings"
    fi
}

#
# Uses dpdk-devbind.py to move devices to work with kernel drivers again
#
unbind_devices()
{
    DRV=$1
    PCI_PATH=$2
    ${RTE_SDK}/usertools/dpdk-devbind.py -b $DRV $PCI_PATH || LOG_ERR "[Fail] unbind"
    LOG_INFO "unbind $PCI_PATH, bind to driver $DRV Successfully !"
}

#
# 1)original eth info variable eg:
#   if1=enp5s0f0
#   bus1=0000:05:00.0
#   driver1=ixgbe
# 2)current eth info variable eg:
#   Slot1=0000:05:00.0
#   Interface1=enp5s0f0
#   Linking1=1
#   Driver1="ixgbe"
#
get_all_eth_info()
{
    if [ ! -f ./.if_tbl ]
    then
        LOG_ERR "ERROR: there is no .if_tbl, please do ./dpdk.install"
        exit
    fi

    # calc original eth info from .if_tbl
    original_eth_info=$(awk '{print gensub(/=(\S*)/, cnt++ "=\\1;", "g");} END{print "dev_cnt_in_tbl=" cnt;}' ./.if_tbl)
    eval $original_eth_info

    # get eth dev info from lspci and format the output
    # as shell script's value assignment.
    eth_info=$(lspci -Dvmmnnk | awk -v RS="\n\n" '/Eth/{print gensub(/:\s([^\n]*)/, cnt++ "=\"\\1\"", "g");} END{print "dev_cnt_in_pci=" cnt;}')

    # eval the shell script to get the variables.
    eval $eth_info

    # variables eg:
    # Slot2="0000:05:00.1"
    # Class2="Ethernet controller [0200]"
    # Vendor2="Intel Corporation [8086]"
    # Device2="82599ES 10-Gigabit SFI/SFP+ Network Connection [10fb]"
    # PhySlot2="0-2"
    # Rev2="01"
    # Driver2="ixgbe"

    # 获取所有 pci 网卡的 Linking 状态，记录在 $Linking<i> 中
    local i=0;
    for ((i=0; i<$dev_cnt_in_pci; i++))
    do
        # calculate eth interface and linking status
        # if eth1's Slog is 0000:05:00.0
        # it's interface name is /sys/bus/pci/devices/0000:05:00.0/net/[interface]
        # it's linking status in /sys/bus/pci/devices/0000:05:00.0/net/[interface]/carrier
        eval "Interface$i=---"
        eval "Linking$i=-"
        eval "dev_net_path=/sys/bus/pci/devices/\$Slot$i/net"
        if [ -e $dev_net_path ]
        then
            eval "Interface$i=$(ls $dev_net_path)"
            eval "read Linking$i</sys/bus/pci/devices/\$Slot$i/net/\$Interface$i/carrier 2>/dev/null"
        fi
    done
}

#
# ret info at pci_addr_of_this_eth and driver_of_this_eth
#
get_eth_original_info_by_name()
{
    dst_eth_name=$1
    pci_addr_of_this_eth=""
    driver_of_this_eth=""

    # 原始网卡信息记录在 .if_tbl 中，遍历 .if_tbl 条目寻找 dst_eth_name
    local i=0;
    for((i=0; i<$dev_cnt_in_tbl; i++))
    do
        eval "eth_name_i=\$if$i"

        if [ -z "$eth_name_i" ]
        then
          return 1
        fi

        if [ "$eth_name_i" == $dst_eth_name ]
        then
            eval "pci_addr_of_this_eth=\$bus$i"
            eval "driver_of_this_eth=\$driver$i"
            return 0
        fi
    done
    return 1
}

#
# ret info at driver_of_this_eth
#
get_eth_original_info_by_pci()
{
    driver_of_this_eth=""

    # 原始网卡信息记录在 .if_tbl 中，遍历 .if_tbl 条目寻找 pci 地址为 $1 的
    local i=0;
    for((i=0; i<$dev_cnt_in_tbl; i++))
    do
        eval "pci_i=\$bus$i"

        if [ -z $pci_i ]
        then
          return 1
        fi

        if [ $pci_i == $1 ]
        then
            eval "driver_of_this_eth=\$driver$i"
            return 0
        fi
    done
    return 1
}

#
# bind all given eth to dpdk driver.
# zero args will do nothing.
#
function bind_devices_by_name
{
    for eth in $*
    do
        get_eth_original_info_by_name $eth
        if [ $? -ne 0 ]
        then
            echo "can not get pci addr of $eth, bind failed."
            continue
        fi
        echo "bind eth $eth to dpdk driver igb_uio ..."
        bind_devices_to_igb_uio $pci_addr_of_this_eth
    done
}

#
# unbind a eth to it's original driver
#
function unbind_this_device_by_name
{
    get_eth_original_info_by_name $1
    if [ $? -ne 0 ]
    then
        echo "can not get pci addr of $eth, bind failed."
        return 0
    fi

    unbind_devices $driver_of_this_eth $pci_addr_of_this_eth
}

#
# 1) unbind given eth to their original driver.
# 2) if no eth given, unbind all eth binding dpdk driver.
#
function unbind_devices_by_name
{
    # unbind all given eth
    for eth in $*
    do
        echo "unbind dev for $eth"
        unbind_this_device_by_name $eth
    done

    # given at least one eth, bind done and ret
    if [ $# -gt 0 ]
    then
        return 0
    fi

    # given no eth, unbind all dev binding dpdk driver
    # 在当前 pci 网卡设备中遍历寻找其 Driver 为 "igb_uio" 的设备，
    # 发现即为其解绑.
    local i=0;
    for((i=0; i<$dev_cnt_in_pci; i++))
    do
        eval "Driver_i=\$Driver$i"

        if [ -z $Driver_i ]
        then
          return 1
        fi

        if [ $Driver_i != "igb_uio" ]
        then
            continue
        fi

        eval "get_eth_original_info_by_pci \$Slot$i"
        if [ $? -ne 0 ]
        then
            eval "echo can not get driver of \$Slot$i, unbind failed."
            continue
        fi

        eval "unbind_devices $driver_of_this_eth \$Slot$i"
    done
}

show_config()
{
    ##############################################
    #                                            #
    #               TODO                         #
    #                                            #
    ##############################################
    LOG_WAR "configed eth is \"$ETH_NAME_LIST\""
    LOG_WAR "need huge page total size is $HUGE_PAGE_TOTAL_SIZE_IN_KB kB, \
huge page size is $HUGEPGSZ_NUMBER kB, need $NUMA_HUGE_PAGE_CNT page."
    echo
}

################################################################ function definition ends. ################################################################

# get all eth info from .if_tbl and lspci
get_all_eth_info

# bind default eth dev if there is no arguments and options
if [ $# -eq 0 ]
then
    show_config

    # check ETH_NAME_LIST
    if [ "$ETH_NAME_LIST" == "XXX YYY" ]
    then
        LOG_ERR "bad eth name \"$ETH_NAME_LIST\", please configure this variable."
        exit
    fi

    # bind eth dev to dpdk driver
    # load dpdk module and set huge pages
    step 1 load_igb_uio_module
    step 2 load_kni_module
    step 3 set_numa_pages $NUMA_HUGE_PAGE_CNT
    step 4 bind_devices_by_name $ETH_NAME_LIST
    exit
fi

# parse opts to do bind or unbind
app_opt="hm:bxc"
while getopts ":"$app_opt opt
do
    case $opt in
        h ) show_config
            echo "$usage_str"

            exit
            ;;

        m )
            HUGE_PAGE_TOTAL_SIZE_IN_KB=$((1024*1024*$OPTARG))
            NUMA_HUGE_PAGE_CNT=$((HUGE_PAGE_TOTAL_SIZE_IN_KB/HUGEPGSZ_NUMBER))
            ;;

        b ) echo "bind dpdk's igb_uio driver to eth dev"
            # make ramains all eths.
            # "-m 20 -b ens192" => "ens192"
            shift $(($OPTIND - 1))

            show_config
            # load dpdk module and set huge pages
            step 1 load_igb_uio_module
            step 2 load_kni_module
            step 3 set_numa_pages $NUMA_HUGE_PAGE_CNT

            # bind given eth dev to dpdk driver
            step 4 bind_devices_by_name $*
            step 5 show_numa_pages
            ;;

        x ) echo "unbind dpdk's igb_uio driver and everything for using dpdk."
            shift 1

            show_config
            # unbind all given eth dev to original driver
            step 1 unbind_devices_by_name $*

            # if no eth dev given, remove dpdk module and clear huge pages
            if [ $# -eq 0 ]
            then
                step 2 remove_igb_uio_module
                step 3 remove_kni_module
                step 4 clear_huge_pages
                step 5 show_numa_pages
            fi
            ;;

        c ) show_numa_pages
            ;;

        ? ) echo "error opt, valid opt is \"$app_opt\""
            exit 1
            ;;
    esac
done
