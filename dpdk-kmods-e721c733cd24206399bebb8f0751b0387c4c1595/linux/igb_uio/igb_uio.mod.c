#include <linux/module.h>
#define INCLUDE_VERMAGIC
#include <linux/build-salt.h>
#include <linux/elfnote-lto.h>
#include <linux/export-internal.h>
#include <linux/vermagic.h>
#include <linux/compiler.h>

#ifdef CONFIG_UNWINDER_ORC
#include <asm/orc_header.h>
ORC_HEADER;
#endif

BUILD_SALT;
BUILD_LTO_INFO;

MODULE_INFO(vermagic, VERMAGIC_STRING);
MODULE_INFO(name, KBUILD_MODNAME);

__visible struct module __this_module
__section(".gnu.linkonce.this_module") = {
	.name = KBUILD_MODNAME,
	.init = init_module,
#ifdef CONFIG_MODULE_UNLOAD
	.exit = cleanup_module,
#endif
	.arch = MODULE_ARCH_INIT,
};

#ifdef CONFIG_RETPOLINE
MODULE_INFO(retpoline, "Y");
#endif



static const struct modversion_info ____versions[]
__used __section("__versions") = {
	{ 0x64674614, "uio_event_notify" },
	{ 0x8fb704cd, "pci_check_and_mask_intx" },
	{ 0x92d5838e, "request_threaded_irq" },
	{ 0xb5f3eae2, "_dev_info" },
	{ 0x4f97404, "pci_alloc_irq_vectors" },
	{ 0x7f829241, "_dev_notice" },
	{ 0xe06ebf0a, "_dev_err" },
	{ 0x504ed3d4, "__dynamic_dev_dbg" },
	{ 0x7161175e, "pci_irq_vector" },
	{ 0x7dfcb5a0, "pci_set_master" },
	{ 0xded78952, "irq_get_irq_data" },
	{ 0x20e6027d, "pci_cfg_access_lock" },
	{ 0xf6433e60, "pci_cfg_access_unlock" },
	{ 0x7d3512ea, "pci_msi_mask_irq" },
	{ 0xb717cdcd, "pci_intx" },
	{ 0xca7db10c, "pci_msi_unmask_irq" },
	{ 0xde80cd09, "ioremap" },
	{ 0x49941a99, "kmalloc_caches" },
	{ 0x844ba130, "kmalloc_trace" },
	{ 0x4cb8fc54, "pci_enable_device" },
	{ 0xedc03953, "iounmap" },
	{ 0x7b00575b, "pci_disable_device" },
	{ 0x37a0cba, "kfree" },
	{ 0x3c5dcee5, "dma_set_mask" },
	{ 0xd14d146c, "dma_set_coherent_mask" },
	{ 0x1e9036d3, "sysfs_create_group" },
	{ 0x98ddc231, "__uio_register_device" },
	{ 0xeaa82eee, "dma_alloc_attrs" },
	{ 0xb9b813f6, "dma_free_attrs" },
	{ 0x1101917f, "sysfs_remove_group" },
	{ 0xeb936266, "_dev_warn" },
	{ 0xe2d5255a, "strcmp" },
	{ 0x122c3a7e, "_printk" },
	{ 0xea5e6830, "__pci_register_driver" },
	{ 0xb23cf1a5, "pci_unregister_driver" },
	{ 0xdd50a2d4, "uio_unregister_device" },
	{ 0x9aed1f01, "param_ops_int" },
	{ 0xe7bf662d, "param_ops_charp" },
	{ 0xbdfb6dbb, "__fentry__" },
	{ 0x5c3c7387, "kstrtoull" },
	{ 0xb04f106f, "pci_num_vf" },
	{ 0x5b8239ca, "__x86_return_thunk" },
	{ 0xf355ad4b, "pci_disable_sriov" },
	{ 0x94146a1a, "pci_enable_sriov" },
	{ 0xf0fdf6cb, "__stack_chk_fail" },
	{ 0x65487097, "__x86_indirect_thunk_rax" },
	{ 0x656e4a6e, "snprintf" },
	{ 0x7966c270, "pci_clear_master" },
	{ 0xc1514a3b, "free_irq" },
	{ 0x69765627, "pci_free_irq_vectors" },
	{ 0xc7c48db2, "module_layout" },
};

MODULE_INFO(depends, "uio");


MODULE_INFO(srcversion, "EAF710D62AF439C590390EE");
